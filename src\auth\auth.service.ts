import {
  Injectable,
  BadRequestException,
  UnauthorizedException,
} from '@nestjs/common';
import { PrismaService } from '../shared/module/prisma/prisma.service';
import { JwtService } from '@nestjs/jwt';
import { EmailService } from '../shared/services/email.service';
import * as admin from 'firebase-admin';
import { AUTH_TYPE, STATUS } from '@prisma/client';
import { AuthDto, VerifyEmailDto } from './dto/auth.dto';
import { authMessages, userMessage } from '../shared/keys/user.key';
import { generateRandomId } from '../shared/module/bcrypt/bcrypt';

@Injectable()
export class AuthService {
  constructor(
    private prisma: PrismaService,
    private jwtService: JwtService,
    private emailService: EmailService,
  ) {}

  async authenticate(
    firebaseUid: string,
    authType: AUTH_TYPE,
    fcmToken?: string,
    userData?: AuthDto,
  ) {
    try {
      // Get Firebase user data
      const firebaseUser = await admin.auth().getUser(firebaseUid);

      // Check if user exists
      let user = await this.prisma.user.findUnique({
        where: { firebaseUid },
        include: { userSession: true },
      });

      if (!user) {
        // Create new user
        user = await this.prisma.user.create({
          data: {
            firebaseUid,
            email: firebaseUser.email || '',
            emailVerified: firebaseUser.emailVerified || false,
            firstName:
              userData?.firstName ||
              firebaseUser.displayName?.split(' ')[0] ||
              '',
            lastName:
              userData?.lastName ||
              firebaseUser.displayName?.split(' ')[1] ||
              '',
            authType,
            profileImage: firebaseUser.photoURL || null,
          },
          include: { userSession: true },
        });
      }

      // Create or update user session
      let userSession = await this.prisma.userSession.findFirst({
        where: { userId: user.id, status: STATUS.ENABLED },
      });

      if (!userSession) {
        userSession = await this.prisma.userSession.create({
          data: {
            userId: user.id,
            fcmToken: fcmToken || null,
          },
        });
      } else if (fcmToken) {
        userSession = await this.prisma.userSession.update({
          where: { id: userSession.id },
          data: { fcmToken },
        });
      }

      return {
        status: true,
        message: 'Authentication successful',
        user,
        userSession,
      };
    } catch (error) {
      throw new BadRequestException(authMessages.AUTH_ERROR);
    }
  }

  async sendVerificationEmail(firebaseUid: string) {
    const user = await this.prisma.user.findUnique({
      where: { firebaseUid },
    });

    if (!user) {
      throw new BadRequestException(userMessage.USER_NOT_FOUND);
    }

    if (user.emailVerified) {
      throw new BadRequestException('Email is already verified');
    }

    // Check if there's a recent OTP request (prevent spam)
    const recentOtp = await this.prisma.otpVerification.findFirst({
      where: {
        email: user.email,
        status: STATUS.ENABLED,
        createdAt: {
          gte: new Date(Date.now() - 2 * 60 * 1000), // 2 minutes ago
        },
      },
    });

    if (recentOtp) {
      throw new BadRequestException(
        'Please wait 2 minutes before requesting a new OTP',
      );
    }

    // Disable any existing OTPs for this email
    await this.prisma.otpVerification.updateMany({
      where: {
        email: user.email,
        status: STATUS.ENABLED,
      },
      data: {
        status: STATUS.DISABLED,
      },
    });

    // Generate new OTP
    const otp = await generateRandomId();
    const expiresAt = new Date(Date.now() + 10 * 60 * 1000); // 10 minutes

    // Save OTP to database
    await this.prisma.otpVerification.create({
      data: {
        email: user.email,
        otp,
        expiresAt,
      },
    });

    // Send OTP email using Zepto Mail
    const emailSent = await this.emailService.sendOtpEmail(
      user.email,
      otp,
      user.firstName ?? '',
    );

    if (!emailSent) {
      // If email sending fails, disable the OTP
      await this.prisma.otpVerification.updateMany({
        where: {
          email: user.email,
          otp,
          status: STATUS.ENABLED,
        },
        data: {
          status: STATUS.DISABLED,
        },
      });

      throw new BadRequestException(
        'Failed to send verification email. Please try again.',
      );
    }

    return {
      status: true,
      message: 'Verification email sent successfully',
    };
  }

  async verifyEmail(firebaseUid: string, verifyEmailDto: VerifyEmailDto) {
    const user = await this.prisma.user.findUnique({
      where: { firebaseUid },
    });

    if (!user) {
      throw new BadRequestException(userMessage.USER_NOT_FOUND);
    }

    const otpRecord = await this.prisma.otpVerification.findFirst({
      where: {
        email: user.email,
        otp: verifyEmailDto.otp,
        status: STATUS.ENABLED,
        expiresAt: { gt: new Date() },
      },
    });

    if (!otpRecord) {
      throw new BadRequestException(authMessages.ENTER_VALID_OTP);
    }

    // Update user email verification status
    await this.prisma.user.update({
      where: { id: user.id },
      data: { emailVerified: true },
    });

    // Disable OTP
    await this.prisma.otpVerification.update({
      where: { id: otpRecord.id },
      data: { status: STATUS.DISABLED },
    });

    return {
      status: true,
      message: 'Email verified successfully',
    };
  }

  async updateFcmToken(firebaseUid: string, fcmToken: string) {
    const user = await this.prisma.user.findUnique({
      where: { firebaseUid },
    });

    if (!user) {
      throw new BadRequestException(userMessage.USER_NOT_FOUND);
    }

    await this.prisma.userSession.updateMany({
      where: { userId: user.id, status: STATUS.ENABLED },
      data: { fcmToken },
    });

    return {
      status: true,
      message: 'FCM token updated successfully',
    };
  }

  async logout(firebaseUid: string, sessionId?: string) {
    const user = await this.prisma.user.findUnique({
      where: { firebaseUid },
    });

    if (!user) {
      throw new BadRequestException(userMessage.USER_NOT_FOUND);
    }

    if (sessionId) {
      await this.prisma.userSession.update({
        where: { id: sessionId },
        data: { status: STATUS.DISABLED },
      });
    } else {
      await this.prisma.userSession.updateMany({
        where: { userId: user.id, status: STATUS.ENABLED },
        data: { status: STATUS.DISABLED },
      });
    }

    return {
      status: true,
      message: 'Logged out successfully',
    };
  }
}
