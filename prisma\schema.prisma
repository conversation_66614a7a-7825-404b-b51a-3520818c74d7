generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "mysql"
  url      = env("DATABASE_URL")
}

// ================================================================= Enums =================================================================

enum STATUS {
  ENABLED
  DISABLED
}

enum USER_TYPE {
  OWNER
  MEMBER
}

enum AUTH_TYPE {
  EMAIL
  GOOGLE
  APPLE
}

enum WORKOUT_TYPE {
  WEIGHT_TRAINING
  RUNNING
  CYCLING
  CUSTOM_WORKOUT
}

enum REP_TYPE {
  RANGE
  MAX
}

enum CHENNEL_PERMISSION {
  EVERYONE
  OWNER
}

enum MEDIA_CATEGORY {
  TRAINING
  NUTRITION
  MINDSET
  RECOVERY
  OTHER
}

// ================================================================= Models =================================================================

model User {
  id                         String              @id @unique @default(uuid())
  firebaseUid                String              @unique
  email                      String
  emailVerified              Boolean             @default(false)
  firstName                  String?
  lastName                   String?
  type                       USER_TYPE?
  profileImage               String?
  authType                   AUTH_TYPE
  contact                    String?
  bio                        String?             @db.Text
  isPushNotificationsEnabled Boolean             @default(false)
  status                     STATUS              @default(ENABLED)
  isDeleted                  Boolean             @default(false)
  createdAt                  DateTime            @default(now())
  updatedAt                  DateTime            @default(now()) @updatedAt
  profileComplete            Boolean             @default(false)
  userSession                UserSession[]
  notification               Notification[]
  communities                Community[]
  chatChannelAccess          ChatChannelAccess[]
  sentMessages               ChatMessage[]       @relation("UserSentMessages")
}

model OtpVerification {
  id        String   @id @default(uuid())
  email     String
  otp       String
  expiresAt DateTime
  status    STATUS   @default(ENABLED)
  isDeleted Boolean  @default(false)
  createdAt DateTime @default(now())
  updatedAt DateTime @default(now()) @updatedAt

  @@index([email, status])
  @@index([email, otp, status])
}

model UserSession {
  id        String   @id @unique @default(uuid())
  user      User     @relation(fields: [userId], references: [id])
  userId    String
  fcmToken  String?  @db.Text
  status    STATUS   @default(ENABLED)
  isDeleted Boolean  @default(false)
  createdAt DateTime @default(now())
  updatedAt DateTime @default(now()) @updatedAt
}

model Community {
  id           String         @id @unique @default(uuid())
  user         User           @relation(fields: [userId], references: [id])
  userId       String
  name         String
  description  String?        @db.Text
  image        String?
  banner       String?
  videoUrl     String?
  isFree       Boolean        @default(true)
  price        Int?
  members      Int            @default(0)
  dailyPlans   DailyPlan[]
  chatChannels ChatChannel[]
  mediaLibrary MediaLibrary[]

  status    STATUS   @default(ENABLED)
  isDeleted Boolean  @default(false)
  createdAt DateTime @default(now())
  updatedAt DateTime @default(now()) @updatedAt
}

model DailyPlan {
  id          String           @id @default(uuid())
  community   Community        @relation(fields: [communityId], references: [id])
  communityId String
  date        DateTime
  training    TrainingPlan[]
  nutrition   NutritionEntry[]
  other       OtherEntry[]
  tags        TagEntry[]
  status      STATUS           @default(ENABLED)
  isDeleted   Boolean          @default(false)
  createdAt   DateTime         @default(now())
  updatedAt   DateTime         @default(now()) @updatedAt
}

model TrainingPlan {
  id          String     @id @default(uuid())
  dailyPlan   DailyPlan  @relation(fields: [dailyPlanId], references: [id])
  dailyPlanId String
  title       String
  description String?
  image       String?
  exercises   Exercise[]
  status      STATUS     @default(ENABLED)
  isDeleted   Boolean    @default(false)
  createdAt   DateTime   @default(now())
  updatedAt   DateTime   @default(now()) @updatedAt
}

model Exercise {
  id             String       @id @default(uuid())
  trainingPlan   TrainingPlan @relation(fields: [trainingPlanId], references: [id])
  trainingPlanId String
  title          String
  image          String?
  sets           Int
  repsType       REP_TYPE
  minReps        Int?
  maxReps        Int
  rpe            Int
  status         STATUS       @default(ENABLED)
  isDeleted      Boolean      @default(false)
  createdAt      DateTime     @default(now())
  updatedAt      DateTime     @default(now()) @updatedAt
}

model NutritionEntry {
  id           String    @id @default(uuid())
  dailyPlan    DailyPlan @relation(fields: [dailyPlanId], references: [id])
  dailyPlanId  String
  name         String
  type         String
  ingredients  String    @db.Text
  recipe       String?   @db.Text
  mealImage    String?
  externalLink String?
  status       STATUS    @default(ENABLED)
  isDeleted    Boolean   @default(false)
  createdAt    DateTime  @default(now())
  updatedAt    DateTime  @default(now()) @updatedAt
}

model OtherEntry {
  id          String    @id @default(uuid())
  dailyPlan   DailyPlan @relation(fields: [dailyPlanId], references: [id])
  dailyPlanId String
  title       String
  description String    @db.Text
  status      STATUS    @default(ENABLED)
  isDeleted   Boolean   @default(false)
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @default(now()) @updatedAt
}

model TagEntry {
  id          String    @id @default(uuid())
  dailyPlan   DailyPlan @relation(fields: [dailyPlanId], references: [id])
  dailyPlanId String
  note        String    @db.Text
  videoUrl    String?
  status      STATUS    @default(ENABLED)
  isDeleted   Boolean   @default(false)
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @default(now()) @updatedAt
}

model ChatChannel {
  id           String              @id @default(uuid())
  community    Community           @relation(fields: [communityId], references: [id])
  communityId  String
  name         String
  permission   CHENNEL_PERMISSION  @default(EVERYONE)
  allowedUsers ChatChannelAccess[]
  messages     ChatMessage[]
  status       STATUS              @default(ENABLED)
  isDeleted    Boolean             @default(false)
  createdAt    DateTime            @default(now())
  updatedAt    DateTime            @default(now()) @updatedAt
}

model ChatChannelAccess {
  id        String      @id @default(uuid())
  channel   ChatChannel @relation(fields: [channelId], references: [id])
  channelId String
  user      User        @relation(fields: [userId], references: [id])
  userId    String

  status    STATUS   @default(ENABLED)
  isDeleted Boolean  @default(false)
  createdAt DateTime @default(now())
  updatedAt DateTime @default(now()) @updatedAt
}

model ChatMessage {
  id        String      @id @default(uuid())
  channel   ChatChannel @relation(fields: [channelId], references: [id])
  channelId String
  sender    User        @relation("UserSentMessages", fields: [senderId], references: [id])
  senderId  String
  content   String      @db.Text
  isRead    Boolean     @default(false)

  status    STATUS   @default(ENABLED)
  isDeleted Boolean  @default(false)
  createdAt DateTime @default(now())
  updatedAt DateTime @default(now()) @updatedAt
}

model MediaLibrary {
  id          String         @id @default(uuid())
  community   Community      @relation(fields: [communityId], references: [id])
  communityId String
  title       String
  category    MEDIA_CATEGORY
  videoUrl    String
  status      STATUS         @default(ENABLED)
  isDeleted   Boolean        @default(false)
  createdAt   DateTime       @default(now())
  updatedAt   DateTime       @default(now()) @updatedAt
}

model Notification {
  id        String   @id @unique @default(uuid())
  type      String?  @db.Text
  title     String?  @db.Text
  body      String?  @db.Text
  isRead    Boolean? @default(false)
  user      User?    @relation(fields: [userId], references: [id])
  userId    String?
  status    STATUS   @default(ENABLED)
  isDeleted Boolean  @default(false)
  createdAt DateTime @default(now())
  updatedAt DateTime @default(now()) @updatedAt
}

