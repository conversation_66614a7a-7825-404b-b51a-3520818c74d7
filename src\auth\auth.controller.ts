import { Controller, Post, Patch, Delete, Body, Headers, Query, UseGuards, Req } from '@nestjs/common';
import { AuthService } from './auth.service';
import { FirebaseAuthGuard } from './guards/firebase-auth.guard';
import { AuthDto, VerifyEmailDto } from './dto/auth.dto';
import { AUTH_TYPE } from '@prisma/client';
import { ApiTags, ApiBearerAuth, ApiHeader } from '@nestjs/swagger';

@ApiTags('Authentication')
@Controller('auth')
export class AuthController {
  constructor(private readonly authService: AuthService) {}

  @Post()
  @UseGuards(FirebaseAuthGuard)
  @ApiBearerAuth()
  @ApiHeader({ name: 'auth-type', enum: AUTH_TYPE })
  @ApiHeader({ name: 'notification-token', required: false })
  async authenticate(
    @Req() req: any,
    @Headers('auth-type') authType: AUTH_TYPE,
    @Headers('notification-token') fcmToken?: string,
    @Body() authDto?: AuthDto,
  ) {
    return this.authService.authenticate(req.user.uid, authType, fcmToken, authDto);
  }

  @Post('send-verification-email')
  @UseGuards(FirebaseAuthGuard)
  @ApiBearerAuth()
  async sendVerificationEmail(@Req() req: any) {
    return this.authService.sendVerificationEmail(req.user.uid);
  }

  @Patch('verify-email')
  @UseGuards(FirebaseAuthGuard)
  @ApiBearerAuth()
  async verifyEmail(@Req() req: any, @Body() verifyEmailDto: VerifyEmailDto) {
    return this.authService.verifyEmail(req.user.uid, verifyEmailDto);
  }

  @Patch('fcm-token')
  @UseGuards(FirebaseAuthGuard)
  @ApiBearerAuth()
  @ApiHeader({ name: 'notification-token' })
  async updateFcmToken(
    @Req() req: any,
    @Headers('notification-token') fcmToken: string,
  ) {
    return this.authService.updateFcmToken(req.user.uid, fcmToken);
  }

  @Delete('logout')
  @UseGuards(FirebaseAuthGuard)
  @ApiBearerAuth()
  @ApiHeader({ name: 'notification-token', required: false })
  async logout(
    @Req() req: any,
    @Query('sessionId') sessionId?: string,
  ) {
    return this.authService.logout(req.user.uid, sessionId);
  }
}